#!/usr/bin/env python3
"""
Performance test script to verify AI agent response time improvements.

This script tests the chat endpoint to measure response times before and after optimizations.
"""

import asyncio
import time
import statistics
from typing import List
import httpx
import json

# Configuration
BASE_URL = "http://localhost:8000"  # Adjust as needed
TEST_MESSAGE = "Hello, can you help me with hotel booking?"
NUM_REQUESTS = 10
ENDPOINT = "/api/v1/agents/chat"  # Updated to match your actual endpoint


async def test_chat_performance() -> List[float]:
    """Test chat endpoint performance and return response times."""
    response_times = []

    async with httpx.AsyncClient() as client:
        for i in range(NUM_REQUESTS):
            start_time = time.time()

            try:
                response = await client.post(
                    f"{BASE_URL}{ENDPOINT}",
                    json={
                        "message": f"{TEST_MESSAGE} (test {i+1})",
                        "agent_type": "hotel_manager"
                    },
                    timeout=30.0
                )

                end_time = time.time()
                response_time = end_time - start_time
                response_times.append(response_time)

                if response.status_code == 200:
                    print(f"Request {i+1}: {response_time:.3f}s - SUCCESS")
                else:
                    print(
                        f"Request {i+1}: {response_time:.3f}s - ERROR {response.status_code}")

            except Exception as e:
                end_time = time.time()
                response_time = end_time - start_time
                response_times.append(response_time)
                print(f"Request {i+1}: {response_time:.3f}s - EXCEPTION: {e}")

            # Small delay between requests
            await asyncio.sleep(0.5)

    return response_times


def analyze_performance(response_times: List[float]) -> None:
    """Analyze and display performance statistics."""
    if not response_times:
        print("No response times to analyze")
        return

    print("\n" + "="*50)
    print("PERFORMANCE ANALYSIS")
    print("="*50)
    print(f"Total requests: {len(response_times)}")
    print(f"Average response time: {statistics.mean(response_times):.3f}s")
    print(f"Median response time: {statistics.median(response_times):.3f}s")
    print(f"Min response time: {min(response_times):.3f}s")
    print(f"Max response time: {max(response_times):.3f}s")

    if len(response_times) > 1:
        print(f"Standard deviation: {statistics.stdev(response_times):.3f}s")

    # Performance thresholds
    fast_responses = [t for t in response_times if t < 2.0]
    medium_responses = [t for t in response_times if 2.0 <= t < 5.0]
    slow_responses = [t for t in response_times if t >= 5.0]

    print(f"\nResponse time distribution:")
    print(
        f"  Fast (< 2s): {len(fast_responses)} ({len(fast_responses)/len(response_times)*100:.1f}%)")
    print(
        f"  Medium (2-5s): {len(medium_responses)} ({len(medium_responses)/len(response_times)*100:.1f}%)")
    print(
        f"  Slow (≥ 5s): {len(slow_responses)} ({len(slow_responses)/len(response_times)*100:.1f}%)")

    # Performance assessment
    avg_time = statistics.mean(response_times)
    if avg_time < 2.0:
        assessment = "EXCELLENT"
    elif avg_time < 3.0:
        assessment = "GOOD"
    elif avg_time < 5.0:
        assessment = "ACCEPTABLE"
    else:
        assessment = "NEEDS IMPROVEMENT"

    print(f"\nOverall performance: {assessment}")
    print("="*50)


async def main():
    """Main test function."""
    print("Starting AI Agent Performance Test")
    print(f"Testing {NUM_REQUESTS} requests to {BASE_URL}/chat")
    print("-" * 50)

    try:
        response_times = await test_chat_performance()
        analyze_performance(response_times)

        # Save results to file
        results = {
            "timestamp": time.time(),
            "num_requests": NUM_REQUESTS,
            "response_times": response_times,
            "average": statistics.mean(response_times) if response_times else 0,
            "median": statistics.median(response_times) if response_times else 0,
            "min": min(response_times) if response_times else 0,
            "max": max(response_times) if response_times else 0
        }

        with open("performance_results.json", "w") as f:
            json.dump(results, f, indent=2)

        print(f"\nResults saved to performance_results.json")

    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"\nTest failed with error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
