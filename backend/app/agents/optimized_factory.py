"""
Optimized Agent Factory for OpenAI Agents SDK

This module creates lightweight, fast-loading agents that only initialize
the tools they actually need, eliminating the 40+ second delay.

Key optimizations:
1. Lazy tool loading - tools are only loaded when needed
2. Minimal agent instances - no heavy initialization
3. Tool caching - reuse tool instances across requests
4. Fast agent creation - pre-configured agents without database calls
"""

import logging
from typing import Dict, List, Optional, Any
from agents import Agent
from app.core.config import settings

logger = logging.getLogger(__name__)

# Get the model name for Azure OpenAI
model_name = settings.AZURE_OPENAI_MODEL or "gpt-4o-mini"

class OptimizedAgentFactory:
    """Factory for creating optimized agents with minimal initialization time."""
    
    def __init__(self):
        self._tool_cache: Dict[str, Any] = {}
        self._agent_cache: Dict[str, Agent] = {}
        
    def _get_minimal_hotel_tools(self, intent: str = None) -> List[Any]:
        """
        Get only the tools needed for the specific intent.
        This avoids loading all tools and making unnecessary database calls.
        """
        # Import tools only when needed (lazy loading)
        from app.agents.tools.hotel_tools import (
            get_room_prices, 
            list_hotels,
            update_room_price,
            get_update_room_price_form
        )
        
        # Determine which tools are needed based on intent
        if intent and "price" in intent.lower() and "update" in intent.lower():
            # User wants to update prices - need update tools
            return [update_room_price, get_update_room_price_form, get_room_prices]
        elif intent and ("list" in intent.lower() or "hotels" in intent.lower()):
            # User wants to see hotels - need listing tools
            return [list_hotels, get_room_prices]
        elif intent and "price" in intent.lower():
            # User wants to see prices - need price tools
            return [get_room_prices, list_hotels]
        else:
            # Default: provide basic tools for general queries
            return [get_room_prices, list_hotels]
    
    def create_hotel_manager_agent(self, intent: str = None, minimal: bool = True) -> Agent:
        """
        Create an optimized hotel manager agent.
        
        Args:
            intent: Optional intent to determine which tools to load
            minimal: If True, load only essential tools for faster performance
            
        Returns:
            Agent: Optimized agent instance
        """
        cache_key = f"hotel_manager_{intent}_{minimal}"
        
        # Return cached agent if available
        if cache_key in self._agent_cache:
            logger.info(f"Using cached hotel manager agent for intent: {intent}")
            return self._agent_cache[cache_key]
        
        # Get tools based on intent (lazy loading)
        if minimal:
            tools = self._get_minimal_hotel_tools(intent)
            logger.info(f"Loading {len(tools)} minimal tools for intent: {intent}")
        else:
            # Load all tools (slower but more comprehensive)
            from app.agents.tools.hotel_tools import HOTEL_TOOLS
            tools = HOTEL_TOOLS
            logger.info(f"Loading all {len(tools)} hotel tools")
        
        # Create optimized agent with minimal instructions
        agent = Agent(
            name="hotel_manager",
            instructions=self._get_optimized_instructions(),
            tools=tools,
            model=model_name,
        )
        
        # Cache the agent for reuse
        self._agent_cache[cache_key] = agent
        logger.info(f"Created and cached hotel manager agent: {cache_key}")
        
        return agent
    
    def create_form_response_agent(self) -> Agent:
        """Create an optimized form response agent."""
        cache_key = "form_response"
        
        if cache_key in self._agent_cache:
            return self._agent_cache[cache_key]
        
        # Import the form response agent class only when needed
        from app.agents.definitions import FormResponseAgent
        
        agent = FormResponseAgent()
        self._agent_cache[cache_key] = agent
        
        return agent
    
    def _get_optimized_instructions(self) -> str:
        """Get optimized instructions for hotel manager agent."""
        return """You are an AI assistant for HM Hotel Management System. 

Your primary role is to assist with hotel room management efficiently:

CORE FUNCTIONS:
- Check room availability and pricing using get_room_prices()
- List available hotels using list_hotels()  
- Update room prices using update_room_price()
- Generate forms for price updates using get_update_room_price_form()

RESPONSE GUIDELINES:
- Be concise and helpful
- Always use tools to get current data
- For price updates, offer to generate a form
- Format responses clearly with proper structure

PERFORMANCE NOTES:
- Use tools efficiently - only call what you need
- Provide quick, accurate responses
- Focus on the user's specific request"""

    def get_agent_by_type(self, agent_type: str, intent: str = None) -> Agent:
        """
        Get an optimized agent by type.
        
        Args:
            agent_type: Type of agent ('hotel_manager', 'form_response')
            intent: Optional intent for tool optimization
            
        Returns:
            Agent: Optimized agent instance
        """
        if agent_type == "hotel_manager":
            return self.create_hotel_manager_agent(intent=intent, minimal=True)
        elif agent_type == "form_response":
            return self.create_form_response_agent()
        else:
            # Fallback to hotel manager for unknown types
            logger.warning(f"Unknown agent type: {agent_type}, using hotel_manager")
            return self.create_hotel_manager_agent(intent=intent, minimal=True)
    
    def clear_cache(self):
        """Clear the agent cache (useful for testing or memory management)."""
        self._agent_cache.clear()
        self._tool_cache.clear()
        logger.info("Cleared agent and tool caches")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics for monitoring."""
        return {
            "cached_agents": len(self._agent_cache),
            "cached_tools": len(self._tool_cache)
        }

# Create a singleton instance
optimized_agent_factory = OptimizedAgentFactory()

def create_fast_agent(agent_type: str = "hotel_manager", intent: str = None) -> Agent:
    """
    Quick function to create a fast, optimized agent.
    
    Args:
        agent_type: Type of agent to create
        intent: Optional intent for tool optimization
        
    Returns:
        Agent: Optimized agent ready for use
    """
    return optimized_agent_factory.get_agent_by_type(agent_type, intent)

def analyze_intent(message: str) -> str:
    """
    Simple intent analysis to determine which tools to load.
    
    Args:
        message: User message
        
    Returns:
        str: Detected intent
    """
    message_lower = message.lower()
    
    if "update" in message_lower and "price" in message_lower:
        return "update_price"
    elif "list" in message_lower and ("hotel" in message_lower or "hotels" in message_lower):
        return "list_hotels"
    elif "price" in message_lower or "cost" in message_lower:
        return "get_prices"
    elif "form" in message_lower:
        return "form_response"
    else:
        return "general"
