from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, Tuple, Callable, Awaitable, List, Union, cast
import uuid
import logging
import time
import asyncio

from agents import Agent, Runner
from agents.tracing import set_tracing_disabled
from fastapi import HTTPException, status
from openai import APIError

from app.core.config import settings
from app.core.openai_config import azure_openai_client
from app.core.types import AgentResponse
from app.agents.definitions import hotel_manager_agent, FormResponseAgent
from app.agents.safety import check_input_safety, SafetyCheckResult
from app.core.localization import localization_service
from .supabase_tracing_service import supabase_tracing_service as tracing_service
from .tracing_service import trace_agent_run
from .database_service import DatabaseService
from app.core.security import verify_supabase_jwt
from app.core.sessions import SupabaseSession, ReadOnlySupabaseSession, CachedReadOnlySupabaseSession
from app.schemas.agent import (
    AgentCreate, AgentUpdate, AgentInDB,
    MessageCreate, MessageInDB,
    SessionCreate, SessionInDB,
    PaginatedMessages
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AgentService:
    """
    Enhanced Agent Service with safety checks and orchestration

    This service manages AI agents, handles input validation, and orchestrates
    the flow between different components of the agent system using Supabase.
    """

    def __init__(self):
        self.agents: Dict[str, Agent] = {}
        self._initialized = False
        self._safety_checks_enabled = settings.SAFETY_CHECKS_ENABLED
        self._max_retries = settings.DEFAULT_MAX_RETRIES
        self._retry_delay = settings.DEFAULT_RETRY_DELAY
        # Cache for agent IDs to avoid database lookups
        self._agent_id_cache: Dict[str, str] = {}
        self._cache_lock = asyncio.Lock()
        # Cache for conversation history to avoid repeated database fetches
        self._conversation_cache: Dict[str, List[dict]] = {}
        self._conversation_cache_lock = asyncio.Lock()
        self._conversation_cache_ttl: Dict[str, float] = {}  # TTL timestamps

    async def initialize(self):
        """Initialize the agent service asynchronously"""
        if not self._initialized:
            # Initialize Azure OpenAI client
            await azure_openai_client.initialize()
            # Disable tracing globally
            set_tracing_disabled(True)
            # Initialize agents
            self._initialize_agents()
            self._initialized = True

    def _initialize_agents(self):
        """Initialize different types of agents"""
        self.agents["hotel_manager"] = hotel_manager_agent
        self.agents["form_response"] = FormResponseAgent()

    # Agent management methods
    async def create_agent(self, user_id: str, name: str, description: str = None, config: dict = None, access_token: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a new agent

        Args:
            user_id: ID of the user creating the agent
            name: Name of the agent
            description: Optional description
            config: Optional configuration dictionary
            access_token: Optional user access token

        Returns:
            Dict containing the created agent data
        """
        db_service = DatabaseService(access_token=access_token)
        return await db_service.create_agent(
            user_id=user_id,
            agent_data=AgentCreate(
                name=name, description=description, config=config or {})
        )

    async def get_agent(self, agent_id: str, access_token: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get an agent by ID

        Args:
            agent_id: ID of the agent to retrieve
            access_token: Optional user access token

        Returns:
            Agent data if found, None otherwise
        """
        db_service = DatabaseService(access_token=access_token)
        return await db_service.get_agent(agent_id)

    async def get_user_agents(self, user_id: str, access_token: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all agents for a user

        Args:
            user_id: ID of the user
            access_token: Optional user access token

        Returns:
            List of agents
        """
        db_service = DatabaseService(access_token=access_token)
        return await db_service.get_user_agents(user_id)

    def get_agent(self, agent_type: str = "hotel_manager") -> Agent:
        """Get an agent by type"""
        return self.agents.get(agent_type, self.agents["hotel_manager"])

    async def get_or_create_session(self, session_id: Optional[str] = None, user_id: Optional[str] = None, access_token: Optional[str] = None) -> SessionInDB:
        """
        Get an existing session or create a new one if it doesn't exist

        Args:
            session_id: Optional session ID to look up
            user_id: Optional user ID to associate with a new session
            access_token: Optional user access token

        Returns:
            SessionInDB: The existing or newly created session
        """
        db_service = DatabaseService(access_token=access_token)
        if session_id:
            session = await db_service.get_session(session_id)
            if session:
                return SessionInDB(**session.dict())

        # Create new session if not found or no session_id provided
        if not user_id:
            user_id = "anonymous"

        expires_at = datetime.utcnow() + timedelta(hours=settings.SESSION_EXPIRE_HOURS)
        session_data = await db_service.create_session(
            SessionCreate(
                user_id=user_id,
                expires_at=expires_at
            )
        )
        return SessionInDB(**session_data.dict())

    def _select_agent(self, input_data: Dict[str, Any]) -> Tuple[Agent, Dict[str, Any]]:
        """Select the appropriate agent based on input data.

        Args:
            input_data: Dictionary containing input data for agent selection

        Returns:
            Tuple of (selected_agent, context_update)

        Raises:
            ValueError: If no suitable agent can be found
        """
        # Get the message from input data
        message = input_data.get('message', '').lower()

        try:
            # Detect language and get instructions
            lang = localization_service.detect_language(message)
            instructions = localization_service.get_translation(
                lang, "hotel_manager_agent")

            if not instructions:
                logger.warning(
                    f"No instructions found for language: {lang}. Falling back to English.")
                lang = "en"
                instructions = localization_service.get_translation(
                    lang, "hotel_manager_agent")

        except ValueError as e:
            logger.warning(
                f"Language detection failed: {e}. Falling back to English.")
            lang = "en"
            instructions = localization_service.get_translation(
                lang, "hotel_manager_agent")

        if not instructions:
            raise ValueError("Default language instructions not found.")

        # Get agent and set instructions
        agent = self.get_agent("hotel_manager")
        agent.instructions = instructions['instructions']

        context_update = {}

        # Add any additional context based on the message
        if any(keyword in message for keyword in ["technical", "bug", "error", "issue"]):
            context_update['needs_technical_support'] = True

        return agent, context_update

    async def _check_safety(self, input_data: Dict[str, Any]) -> Any:
        """
        Perform safety checks on the input data

        Args:
            input_data: Dictionary containing input data to check

        Returns:
            SafetyCheckResult with the result of the safety check
        """
        from app.agents.safety import check_input_safety
        return check_input_safety(input_data)

    async def chat(
        self,
        message: str,
        session_id: Optional[str] = None,
        agent_type: str = "hotel_manager",
        user_id: Optional[str] = None,
        background_tasks: Optional[Any] = None,
        access_token: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Process a chat message with an agent.

        Args:
            message: The message to process
            session_id: Optional session ID for conversation continuity
            agent_type: Type of agent to use (default: "hotel_manager")
            user_id: Optional user ID for the request
            background_tasks: Optional FastAPI background tasks
            **kwargs: Additional arguments to pass to the agent

        Returns:
            Dict containing the agent's response and metadata

        Raises:
            HTTPException: If there's an error processing the message
        """
        try:
            # Use provided session_id or generate a temporary one for immediate response
            if not session_id:
                session_id = str(uuid.uuid4())

            # Set default user ID if not provided
            if not user_id:
                user_id = "anonymous"

            # 1. Prepare input data
            input_data = {
                'user_id': user_id,
                'message': message,
                'session_id': session_id,
                **kwargs
            }

            # 2. Perform safety checks
            safety_result = await self._check_safety(input_data)
            if not safety_result.passed:
                return {
                    'success': False,
                    'error': f"Safety check failed: {safety_result.reason}",
                    'session_id': session_id,
                    'timestamp': datetime.utcnow().isoformat(),
                    'metadata': safety_result.metadata
                }

            # 3. Select the appropriate agent
            try:
                agent, context_update = self._select_agent(
                    safety_result.sanitized_input or input_data)
            except Exception as e:
                return {
                    'success': False,
                    'error': f"Failed to select agent: {str(e)}",
                    'session_id': session_id,
                    'timestamp': datetime.utcnow().isoformat()
                }

            # 4. Process with the selected agent
            context = {
                'user_id': user_id,
                'session_id': session_id,
                'message': message,
                'access_token': access_token,
                **kwargs,
                **context_update,
                'safety_metadata': safety_result.metadata
            }

            # 4. Process with agent to get the response without blocking
            response_text = await self._get_agent_response(agent, message, context)

            # 5. Add background task to save messages and traces (non-blocking)
            if background_tasks:
                background_tasks.add_task(
                    self._save_messages_and_traces_with_session,
                    user_id=user_id,
                    session_id=session_id,
                    message=message,
                    response=response_text,
                    agent_name=agent.name,
                    access_token=access_token,
                    safety_metadata=safety_result.metadata
                )

            # 6. Return the response immediately
            return {
                'success': True,
                'response': response_text,
                'session_id': session_id,
                'timestamp': datetime.utcnow().isoformat(),
                'agent_type': agent.name,
            }

        except Exception as e:
            error_msg = f"Error processing message: {str(e)}"
            logger.error(error_msg, exc_info=True)

            # Log the error for monitoring
            if tracing_service:
                tracing_service.record_error({
                    'error_type': type(e).__name__,
                    'error_message': str(e),
                    'traceback': str(e.__traceback__) if hasattr(e, '__traceback__') else None,
                    'context': {
                        'input': message,
                        'user_id': user_id,
                        **kwargs
                    },
                    'session_id': session_id,
                    'user_id': user_id
                })

            return {
                'success': False,
                'error': "An error occurred while processing your request",
                'session_id': session_id,
                'timestamp': datetime.utcnow().isoformat(),
                'internal_error': str(e) if settings.DEBUG else None
            }

    async def _get_agent_response(self, agent: Agent, message: str, context: Dict[str, Any]) -> str:
        """Get the agent's response without blocking on session operations."""
        session_id = context.get('session_id')
        access_token = context.get('access_token')

        # Get agent ID from cache (fast) or database (slower but cached)
        agent_id = await self._get_agent_id(agent.name, access_token)

        # Get cached conversation history (this is the key optimization!)
        cached_history = await self._get_cached_conversation_history(session_id, agent_id, access_token)

        # Create session data for the agent
        try:
            session_data = await self._get_session_for_history(session_id, context.get('user_id'), access_token)
        except Exception as e:
            logger.warning(
                f"Could not load session data for {session_id}: {e}, using minimal session")
            from app.schemas.agent import SessionInDB
            session_data = SessionInDB(
                id=session_id,
                user_id=context.get('user_id', 'anonymous'),
                expires_at=datetime.utcnow() + timedelta(hours=24),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )

        # Use cached session that doesn't make database calls
        cached_session = CachedReadOnlySupabaseSession(
            session_data=session_data,
            agent_id=agent_id,
            access_token=access_token,
            cached_history=cached_history
        )

        logger.info(
            f"Running agent with {len(cached_history)} cached messages")
        response = await Runner.run(agent, message, session=cached_session)
        return response.final_output if hasattr(response, 'final_output') else str(response)

    async def _get_session_for_history(self, session_id: str, user_id: Optional[str], access_token: Optional[str]) -> SessionInDB:
        """Get session for conversation history, with fast timeout."""
        db_service = DatabaseService(access_token=access_token)

        # Try to get existing session quickly
        session = await db_service.get_session(session_id)
        if session:
            return SessionInDB(**session.dict())

        # If session doesn't exist, create a minimal one for this request
        # The actual session will be created properly in background
        return SessionInDB(
            id=session_id,
            user_id=user_id or 'anonymous',
            expires_at=datetime.utcnow() + timedelta(hours=24),
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

    async def _save_messages_and_traces_with_session(
        self,
        user_id: str,
        session_id: str,
        message: str,
        response: str,
        agent_name: str,
        access_token: Optional[str],
        safety_metadata: Dict[str, Any]
    ):
        """Save messages and traces in background, ensuring session exists."""
        try:
            # Get agent ID from cache (should be fast now)
            agent_id = await self._get_agent_id(agent_name, access_token)

            # Ensure session exists (create if needed)
            session_data = await self.get_or_create_session(session_id, user_id, access_token=access_token)
            session = SupabaseSession(session_data, agent_id, access_token)

            # Save user message and assistant response
            await session.add_items([
                {"role": "user", "content": message},
                {"role": "assistant", "content": response}
            ])

            # Invalidate conversation cache since we added new messages
            await self._invalidate_conversation_cache(session_id, agent_id)

            # Add tracing and metrics (fire-and-forget for performance)
            if tracing_service:
                trace_data = {
                    'user_id': user_id,
                    'input': message[:500],  # Limit input size for performance
                    # Limit output size
                    'output': response[:1000] if isinstance(response, str) else str(response)[:1000],
                    'agent_id': agent_name,
                    'safety_checks': safety_metadata,
                    'success': True
                }
                # Fire-and-forget: don't await the tracing operation
                asyncio.create_task(tracing_service.store_trace_async(
                    trace_data=trace_data,
                    session_id=session_id,
                    user_id=user_id
                ))
        except Exception as e:
            logger.error(
                f"Error saving messages and traces: {str(e)}", exc_info=True)

    # Test methods for performance isolation
    async def _test_agent_direct(self, message: str, agent_type: str = "hotel_manager") -> Dict[str, Any]:
        """
        Direct agent test without any authentication, session management, or data storage.
        This isolates pure AI agent performance.
        """
        try:
            logger.info(
                f"Direct agent test starting for message: {message[:50]}...")

            # Ensure agent service is initialized
            if not self._initialized:
                await self.initialize()

            # Get agent directly
            agent = self.get_agent(agent_type)
            if not agent:
                raise ValueError(f"Agent {agent_type} not found")

            # Create minimal context
            context = {
                'user_id': 'test_user',
                'session_id': 'test_session',
                'message': message,
                'test_mode': True
            }

            # Call agent directly without any database operations
            logger.info("Calling agent directly...")
            response = await Runner.run(agent, message, session=None)
            response_text = response.final_output if hasattr(
                response, 'final_output') else str(response)

            logger.info(
                f"Direct agent response received: {len(response_text)} characters")

            return {
                'success': True,
                'response': response_text,
                'session_id': 'test_session',
                'agent_type': agent.name,
                'timestamp': datetime.utcnow().isoformat(),
            }

        except Exception as e:
            logger.error(
                f"Error in direct agent test: {str(e)}", exc_info=True)
            raise

    async def _test_agent_with_session(self, message: str, session_id: str, agent_type: str = "hotel_manager") -> Dict[str, Any]:
        """
        Test agent with minimal session handling but no data storage.
        """
        try:
            logger.info(
                f"Agent test with session starting for message: {message[:50]}...")

            # Ensure agent service is initialized
            if not self._initialized:
                await self.initialize()

            # Get agent
            agent = self.get_agent(agent_type)
            if not agent:
                raise ValueError(f"Agent {agent_type} not found")

            # Create minimal session for history (but don't save anything)
            from app.schemas.agent import SessionInDB
            minimal_session = SessionInDB(
                id=session_id,
                user_id='test_user',
                expires_at=datetime.utcnow() + timedelta(hours=24),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )

            # Create read-only session that doesn't do any database operations
            class TestSession:
                def __init__(self, session_data):
                    self.session_data = session_data

                async def get_items(self):
                    # Return empty history for test
                    return []

                async def add_items(self, items):
                    # No-op for test
                    pass

                def __getattr__(self, name):
                    return getattr(self.session_data, name)

            test_session = TestSession(minimal_session)

            # Call agent with test session
            logger.info("Calling agent with test session...")
            response = await Runner.run(agent, message, session=test_session)
            response_text = response.final_output if hasattr(
                response, 'final_output') else str(response)

            logger.info(
                f"Agent with session response received: {len(response_text)} characters")

            return {
                'success': True,
                'response': response_text,
                'session_id': session_id,
                'agent_type': agent.name,
                'timestamp': datetime.utcnow().isoformat(),
            }

        except Exception as e:
            logger.error(
                f"Error in agent test with session: {str(e)}", exc_info=True)
            raise

    async def _get_cached_conversation_history(self, session_id: str, agent_id: str, access_token: Optional[str]) -> List[dict]:
        """Get conversation history from cache or database with intelligent caching."""
        cache_key = f"{session_id}:{agent_id}"
        current_time = time.time()
        cache_ttl_seconds = 300  # 5 minutes cache TTL

        async with self._conversation_cache_lock:
            # Check if we have cached data that's still valid
            if (cache_key in self._conversation_cache and
                cache_key in self._conversation_cache_ttl and
                    current_time - self._conversation_cache_ttl[cache_key] < cache_ttl_seconds):
                logger.info(
                    f"Using cached conversation history for session {session_id}")
                return self._conversation_cache[cache_key]

        # Cache miss or expired - fetch from database
        logger.info(
            f"Fetching conversation history from database for session {session_id}")
        try:
            db_service = DatabaseService(access_token=access_token)

            # Fetch with timeout to prevent hanging
            messages, _ = await asyncio.wait_for(
                db_service.get_messages(
                    agent_id=agent_id,
                    session_id=session_id,
                    limit=20  # Limit to recent messages
                ),
                timeout=2.0  # 2 second timeout
            )

            # Convert to the format expected by agents
            conversation_history = [
                {"role": msg.role, "content": msg.content} for msg in messages]

            # Cache the result
            async with self._conversation_cache_lock:
                self._conversation_cache[cache_key] = conversation_history
                self._conversation_cache_ttl[cache_key] = current_time

            logger.info(
                f"Cached {len(conversation_history)} messages for session {session_id}")
            return conversation_history

        except asyncio.TimeoutError:
            logger.warning(
                f"Timeout fetching history for session {session_id}, using empty history")
            # Cache empty result to avoid repeated timeouts
            async with self._conversation_cache_lock:
                self._conversation_cache[cache_key] = []
                self._conversation_cache_ttl[cache_key] = current_time
            return []
        except Exception as e:
            logger.warning(
                f"Error fetching history for session {session_id}: {e}, using empty history")
            return []

    async def _invalidate_conversation_cache(self, session_id: str, agent_id: str):
        """Invalidate conversation cache when new messages are added."""
        cache_key = f"{session_id}:{agent_id}"
        async with self._conversation_cache_lock:
            if cache_key in self._conversation_cache:
                del self._conversation_cache[cache_key]
            if cache_key in self._conversation_cache_ttl:
                del self._conversation_cache_ttl[cache_key]
        logger.debug(
            f"Invalidated conversation cache for session {session_id}")

    async def _get_agent_id(self, agent_name: str, access_token: Optional[str] = None) -> str:
        """Get agent ID from cache or database, creating it if it doesn't exist."""
        # Check cache first
        async with self._cache_lock:
            if agent_name in self._agent_id_cache:
                return self._agent_id_cache[agent_name]

        # Not in cache, fetch from database
        db_service = DatabaseService(access_token=access_token)

        # Try to get the agent by name
        db_agent = await db_service.get_agent_by_name(agent_name)

        if db_agent:
            agent_id = str(db_agent.id)
            # Cache the result
            async with self._cache_lock:
                self._agent_id_cache[agent_name] = agent_id
            return agent_id

        # If agent doesn't exist, create it, but only if we have a valid user
        if not access_token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Cannot create an agent without a valid user session."
            )

        try:
            user_payload = await verify_supabase_jwt(access_token)
            user_id = user_payload.get("sub")
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token: user ID not found."
                )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Token verification failed: {str(e)}"
            )

        new_agent_data = AgentCreate(
            name=agent_name, description=f"System-created agent: {agent_name}")
        created_agent = await db_service.create_agent(user_id=user_id, agent_data=new_agent_data)

        agent_id = str(created_agent.id)
        # Cache the newly created agent ID
        async with self._cache_lock:
            self._agent_id_cache[agent_name] = agent_id
        return agent_id

    async def _with_retry(
        self,
        func: Callable[..., Awaitable[Dict[str, Any]]],
        *args,
        **kwargs
    ) -> Dict[str, Any]:
        """Execute a function with retry logic.

        Args:
            func: The async function to execute
            *args: Positional arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function

        Returns:
            The result of the function call

        Raises:
            Exception: If all retry attempts fail
        """
        last_error = None
        for attempt in range(self._max_retries):
            try:
                return await func(*args, **kwargs)
            except APIError as e:
                last_error = e
                if e.status_code == 429:  # Rate limit
                    wait_time = (2 ** attempt) * self._retry_delay
                    logger.warning(
                        f"Rate limited. Waiting {wait_time} seconds before retry...")
                    await asyncio.sleep(wait_time)
                else:
                    raise
            except Exception as e:
                last_error = e
                if attempt == self._max_retries - 1:
                    break
                wait_time = (2 ** attempt) * self._retry_delay
                logger.warning(
                    f"Attempt {attempt + 1} failed: {str(e)}. Retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)

        # If we get here, all retries failed
        error_msg = f"Failed after {self._max_retries} attempts. Last error: {str(last_error)}"
        logger.error(error_msg)
        raise Exception(error_msg)

    async def add_message(
        self,
        user_id: str,
        agent_id: str,
        content: str,
        role: str,
        metadata: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        access_token: Optional[str] = None
    ) -> MessageInDB:
        """
        Add a message to the conversation

        Args:
            user_id: ID of the user adding the message
            agent_id: ID of the agent
            content: Message content
            role: Role of the message sender ('user' or 'assistant')
            metadata: Optional metadata
            session_id: Optional session ID

        Returns:
            MessageInDB: The created message

        Raises:
            HTTPException: If there's an error creating the message
        """
        try:
            db_service = DatabaseService(access_token=access_token)
            message_data = await db_service.create_message(
                MessageCreate(
                    user_id=user_id,
                    agent_id=agent_id,
                    content=content,
                    role=role,
                    metadata=metadata or {}
                )
            )
            return MessageInDB(**message_data.dict())
        except Exception as e:
            logger.error(f"Error adding message: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to add message to conversation"
            )

    async def get_messages(
        self,
        agent_id: str,
        limit: int = 10,
        cursor: Optional[str] = None,
        session_id: Optional[str] = None,
        access_token: Optional[str] = None
    ) -> Tuple[List[MessageInDB], Optional[str]]:
        """
        Get messages for an agent with pagination

        Args:
            agent_id: ID of the agent
            limit: Maximum number of messages to return (default: 10)
            cursor: Optional cursor for pagination
            session_id: Optional session ID to filter messages

        Returns:
            PaginatedMessages: Paginated list of messages with metadata

        Raises:
            HTTPException: If there's an error retrieving messages
        """
        try:
            db_service = DatabaseService(access_token=access_token)
            messages, next_cursor = await db_service.get_messages(
                agent_id=agent_id,
                session_id=session_id,
                limit=limit,
                cursor=cursor
            )
            return messages, next_cursor
        except Exception as e:
            logger.error(f"Error retrieving messages: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve messages"
            )

    # Session management methods
    async def create_session(
        self,
        user_id: str,
        expires_at: Optional[datetime] = None,
        access_token: Optional[str] = None
    ) -> SessionInDB:
        """
        Create a new user session

        Args:
            user_id: ID of the user
            expires_at: Optional expiration datetime (default: now + SESSION_EXPIRE_HOURS)

        Returns:
            SessionInDB: The created session

        Raises:
            HTTPException: If there's an error creating the session
        """
        try:
            if not expires_at:
                expires_at = datetime.utcnow() + timedelta(hours=settings.SESSION_EXPIRE_HOURS)

            db_service = DatabaseService(access_token=access_token)
            session_data = await db_service.create_session(
                SessionCreate(
                    user_id=user_id,
                    expires_at=expires_at
                )
            )
            return SessionInDB(**session_data.dict())
        except Exception as e:
            logger.error(f"Error creating session: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create session"
            )

    async def get_session(self, session_id: str, access_token: Optional[str] = None) -> Optional[SessionInDB]:
        """
        Get a session by ID

        Args:
            session_id: ID of the session

        Returns:
            SessionInDB if found, None otherwise (does not raise exceptions)
        """
        try:
            db_service = DatabaseService(access_token=access_token)
            session_data = await db_service.get_session(session_id)
            if not session_data:
                return None
            return SessionInDB(**session_data.dict())
        except Exception as e:
            logger.warning(
                f"Could not retrieve session {session_id}: {str(e)}")
            return None  # Return None instead of raising exception

    async def delete_session(self, session_id: str, access_token: Optional[str] = None) -> bool:
        """
        Delete a session and all associated messages

        Args:
            session_id: ID of the session to delete

        Returns:
            bool: True if deletion was successful, False otherwise

        Raises:
            HTTPException: If there's an error deleting the session
        """
        try:
            db_service = DatabaseService(access_token=access_token)
            # First delete all messages in the session
            await db_service.delete_messages_by_session(session_id)

            # Then delete the session itself
            return await db_service.delete_session(session_id)
        except Exception as e:
            logger.error(f"Error deleting session: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete session"
            )


# Global agent service instance
agent_service = AgentService()
