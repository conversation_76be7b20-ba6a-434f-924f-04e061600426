"""Database service for handling all database operations with Supabase.

This service provides a clean interface for interacting with the Supabase database,
handling all CRUD operations for agents, messages, and sessions.
"""
from datetime import datetime, timezone
import logging
from typing import List, Dict, Any, Optional, Tuple, cast
from uuid import UUID, uuid4

from supabase import Client
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, status

from app.core.supabase_client import supabase, supabase_service
from app.core.config import settings
from app.schemas.agent import Agent, AgentCreate, AgentUpdate, Message, MessageCreate, Session, SessionCreate

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class DatabaseService:
    """Service for handling database operations with Supabase.

    This class provides methods for all database interactions, including CRUD operations
    for agents, messages, and sessions. It handles all the low-level Supabase client
    interactions and provides a clean, typed interface for the rest of the application.
    """

    # Table names
    AGENTS_TABLE: str = 'agents'
    MESSAGES_TABLE: str = 'messages'
    SESSIONS_TABLE: str = 'sessions'

    def __init__(self, client: Optional[Client] = None, access_token: Optional[str] = None, use_service_client: bool = False):
        """Initialize the database service.

        Args:
            client: Optional Supabase client instance (for testing)
            access_token: Optional user access token for authenticated requests
            use_service_client: If True, use the service role client to bypass RLS
        """
        if use_service_client:
            self.supabase = supabase_service
        else:
            self.supabase = client or supabase
            if access_token:
                self.supabase.postgrest.auth(access_token)
        self._ensure_tables_exist()

    def _ensure_tables_exist(self) -> None:
        """Ensure all required tables exist in the database."""
        # Supabase automatically creates tables based on schema.sql
        # This is a placeholder for any table validation logic
        pass

    # Agent operations
    async def create_agent(self, user_id: str, agent_data: AgentCreate) -> Agent:
        """Create a new agent.

        Args:
            user_id: ID of the user creating the agent
            agent_data: Agent creation data

        Returns:
            The created agent

        Raises:
            HTTPException: If there's an error creating the agent
        """
        try:
            # Convert Pydantic model to dict and add user_id
            agent_dict = agent_data.dict()
            agent_dict['user_id'] = user_id

            # Insert the agent into the database
            result = self.supabase.table(
                self.AGENTS_TABLE).insert(agent_dict).execute()

            if not result.data:
                logger.error("No data returned when creating agent")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create agent"
                )

            # Convert the raw data to an Agent model
            return Agent(**result.data[0])

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating agent: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while creating the agent"
            )

    async def get_agent_by_name(self, agent_name: str) -> Optional[Agent]:
        """Get an agent by name.

        Args:
            agent_name: Name of the agent to retrieve

        Returns:
            The agent if found, None otherwise
        """
        try:
            result = self.supabase.table(self.AGENTS_TABLE)\
                .select('*')\
                .eq('name', agent_name)\
                .execute()

            if not result.data:
                return None

            return Agent(**result.data[0])

        except Exception as e:
            logger.error(
                f"Error getting agent by name {agent_name}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while retrieving the agent"
            )

    async def get_agent(self, agent_id: str) -> Optional[Agent]:
        """Get an agent by ID.

        Args:
            agent_id: ID of the agent to retrieve

        Returns:
            The agent if found, None otherwise
        """
        try:
            result = self.supabase.table(self.AGENTS_TABLE)\
                .select('*')\
                .eq('id', agent_id)\
                .execute()

            if not result.data:
                return None

            return Agent(**result.data[0])

        except Exception as e:
            logger.error(
                f"Error getting agent {agent_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while retrieving the agent"
            )

    async def get_user_agents(self, user_id: str) -> List[Agent]:
        """Get all agents for a user.

        Args:
            user_id: ID of the user

        Returns:
            List of the user's agents
        """
        try:
            result = self.supabase.table(self.AGENTS_TABLE)\
                .select('*')\
                .eq('user_id', user_id)\
                .order('created_at', desc=True)\
                .execute()

            return [Agent(**agent) for agent in (result.data or [])]

        except Exception as e:
            logger.error(
                f"Error getting agents for user {user_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while retrieving agents"
            )

    # Message operations
    async def create_message(self, user_id: str, agent_id: str, content: str, role: str, metadata: dict, session_id: Optional[str] = None) -> Message:
        """Create a new message.

        Args:
            user_id: The user's ID.
            agent_id: The agent's ID.
            content: The message content.
            role: The message role.
            metadata: The message metadata.
            session_id: The session's ID.

        Returns:
            The created message

        Raises:
            HTTPException: If there's an error creating the message
        """
        try:
            message_dict = {
                "user_id": user_id,
                "agent_id": agent_id,
                "content": content,
                "role": role,
                "metadata": metadata,
                "session_id": session_id,
            }
            # Insert the message into the database
            result = self.supabase.table(self.MESSAGES_TABLE)\
                .insert(message_dict)\
                .execute()

            if not result.data:
                logger.error("No data returned when creating message")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create message"
                )

            # Convert the raw data to a Message model
            return Message(**result.data[0])

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating message: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while creating the message"
            )

    async def create_messages_batch(self, messages_data: List[Dict[str, Any]]) -> List[Message]:
        """Create multiple messages in a single batch operation for better performance.

        Args:
            messages_data: List of message dictionaries to insert

        Returns:
            List of created Message objects

        Raises:
            HTTPException: If there's an error creating the messages
        """
        if not messages_data:
            return []

        try:
            # Insert all messages in a single batch operation
            result = self.supabase.table(self.MESSAGES_TABLE)\
                .insert(messages_data)\
                .execute()

            if not result.data:
                logger.error("No data returned when creating batch messages")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create batch messages"
                )

            # Convert the raw data to Message models
            return [Message(**msg) for msg in result.data]

        except HTTPException:
            raise
        except Exception as e:
            logger.error(
                f"Error creating batch messages: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while creating batch messages"
            )

    async def get_messages(
        self,
        agent_id: str,
        session_id: Optional[str] = None,
        limit: int = 10,
        cursor: Optional[str] = None
    ) -> Tuple[List[Message], Optional[str]]:
        """Get messages for an agent with pagination.

        Args:
            agent_id: ID of the agent
            session_id: Optional ID of the session to filter messages by
            limit: Maximum number of messages to return (1-100)
            cursor: Cursor for pagination (ISO format timestamp)

        Returns:
            A tuple of (messages, next_cursor) where next_cursor is None if there are no more messages

        Raises:
            HTTPException: If there's an error retrieving messages
        """
        # Validate limit
        limit = max(1, min(limit, 100))  # Ensure limit is between 1 and 100

        try:
            # Build the base query
            query = self.supabase.table(self.MESSAGES_TABLE)\
                .select('*')\
                .eq('agent_id', agent_id)

            if session_id:
                query = query.eq('session_id', session_id)

            query = query.order('created_at', desc=True)\
                .limit(limit + 1)  # Fetch one extra to check for more
            # Apply cursor if provided
            if cursor:
                query = query.lt('created_at', cursor)

            # Execute the query
            result = query.execute()
            messages_data = result.data or []

            # Check if there are more messages
            has_more = len(messages_data) > limit

            # If we fetched an extra message, remove it and set the next cursor
            if has_more:
                messages_data = messages_data[:-1]
                next_cursor = messages_data[-1]['created_at']
            else:
                next_cursor = None

            # Convert to Message models
            messages = [Message(**msg) for msg in messages_data]

            return messages, next_cursor

        except Exception as e:
            logger.error(
                f"Error getting messages for agent {agent_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while retrieving messages"
            )

    # Session operations
    async def create_session(self, session_data: SessionCreate) -> Session:
        """Create a new user session.

        Args:
            session_data: Session creation data

        Returns:
            The created session

        Raises:
            HTTPException: If there's an error creating the session
        """
        try:
            # Convert Pydantic model to dict and format datetime
            session_dict = session_data.dict()
            if isinstance(session_dict.get('expires_at'), datetime):
                session_dict['expires_at'] = session_dict['expires_at'].isoformat()

            # Insert the session into the database
            result = self.supabase.table(self.SESSIONS_TABLE)\
                .insert(session_dict)\
                .execute()

            if not result.data:
                logger.error("No data returned when creating session")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create session"
                )

            # Convert the raw data to a Session model
            return Session(**result.data[0])

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating session: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while creating the session"
            )

    async def get_session(self, session_id: str) -> Optional[Session]:
        """Get a session by ID.

        Args:
            session_id: ID of the session

        Returns:
            The session if found and not expired, None otherwise
        """
        try:
            result = self.supabase.table(self.SESSIONS_TABLE)\
                .select('*')\
                .eq('id', session_id)\
                .gt('expires_at', datetime.utcnow().isoformat())\
                .execute()

            if not result.data:
                return None

            return Session(**result.data[0])

        except Exception as e:
            logger.warning(
                f"Could not get session {session_id}: {str(e)}")
            return None  # Return None instead of raising exception

    async def delete_session(self, session_id: str) -> bool:
        """Delete a session.

        Args:
            session_id: ID of the session to delete

        Returns:
            True if the session was deleted, False otherwise

        Raises:
            HTTPException: If there's an error deleting the session
        """
        try:
            result = self.supabase.table(self.SESSIONS_TABLE)\
                .delete()\
                .eq('id', session_id)\
                .execute()

            return bool(result.data)

        except Exception as e:
            logger.error(
                f"Error deleting session {session_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while deleting the session"
            )
