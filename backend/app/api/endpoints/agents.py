from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, status, Response, Request
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta, timezone
from pydantic import BaseModel
import uuid
import logging

from app.schemas.agent import ChatRequest, ChatResponse, AgentInfo, SessionInfo, Message, PaginatedMessages
from app.services.agent_service import agent_service
from app.core.security import get_current_user, http_bearer
from app.core.config import settings
from fastapi.security import HTTPAuthorizationCredentials

router = APIRouter()
logger = logging.getLogger(__name__)

# Request models


class CreateAgentRequest(BaseModel):
    name: str
    description: Optional[str] = None
    config: Optional[Dict[str, Any]] = None


class CreateSessionRequest(BaseModel):
    user_id: str
    expires_in_hours: int = 24  # Default to 24 hours


@router.post("/chat", response_model=ChatResponse)
async def chat_with_agent(
    chat_request: ChatRequest,
    response: Response,
    background_tasks: BackgroundTasks,
    fastapi_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """
    Chat with an AI agent

    Args:
        chat_request: Chat request containing message and session info
        response: FastAPI response object
        background_tasks: FastAPI background tasks
        fastapi_request: FastAPI request object
        current_user: Authenticated user info from JWT token

    Returns:
        ChatResponse with the agent's response
    """
    try:
        # Get session ID from request or cookie (non-blocking)
        access_token = credentials.credentials if credentials else None
        session_id = chat_request.session_id or fastapi_request.cookies.get(
            "sessionId")

        # If no session ID, generate one immediately for fast response
        if not session_id:
            session_id = str(uuid.uuid4())
            logger.info(f"Generated new session_id: {session_id}")
            # Set cookie for future requests
            response.set_cookie(
                key="sessionId",
                value=session_id,
                httponly=True,
                samesite="lax",
                expires=datetime.now(timezone.utc) + timedelta(hours=24),
            )
        else:
            logger.info(f"Using existing session_id: {session_id}")

        # Get agent response immediately (session validation happens in background)
        result = await agent_service.chat(
            message=chat_request.message,
            session_id=session_id,
            agent_type=chat_request.agent_type,
            user_id=current_user["id"],
            background_tasks=background_tasks,
            access_token=access_token
        )

        if not result.get("success"):
            raise HTTPException(status_code=500, detail=result.get(
                "error", "Agent failed to process the request."))

        # Ensure the agent_type matches the enum
        agent_type_str = result["agent_type"]
        if agent_type_str == "HM Hotel Manager":
            agent_type_str = "hotel_manager"

        return ChatResponse(
            response=result["response"],
            session_id=session_id,
            agent_type=agent_type_str,
            metadata=result.get("metadata")
        )
    except Exception as e:
        logger.error(f"Error in chat_with_agent: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Agent error: {str(e)}")


@router.get("/agents", response_model=Dict[str, AgentInfo])
async def get_available_agents():
    """
    Get information about available agents

    Returns:
        Dictionary of agent types to their information
    """
    try:
        agents_info = agent_service.get_available_agents()
        return {
            agent_type: AgentInfo(
                name=info["name"],
                description=info["description"],
                capabilities=info["capabilities"]
            )
            for agent_type, info in agents_info.items()
        }
    except Exception as e:
        logger.error(
            f"Error getting available agents: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error getting available agents: {str(e)}")


@router.post("/agents", status_code=status.HTTP_201_CREATED)
async def create_agent(
    agent_data: CreateAgentRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """
    Create a new agent

    Args:
        agent_data: Agent creation data
        current_user: Authenticated user info from JWT token

    Returns:
        Created agent data
    """
    try:
        access_token = credentials.credentials if credentials else None
        agent = await agent_service.create_agent(
            user_id=current_user["id"],
            name=agent_data.name,
            description=agent_data.description,
            config=agent_data.config or {},
            access_token=access_token
        )
        return agent
    except Exception as e:
        logger.error(f"Error creating agent: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error creating agent: {str(e)}")


@router.get("/agents/my", response_model=List[Dict[str, Any]])
async def get_my_agents(
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """
    Get all agents for the current user

    Args:
        current_user: Authenticated user info from JWT token

    Returns:
        List of user's agents
    """
    try:
        access_token = credentials.credentials if credentials else None
        return await agent_service.get_user_agents(current_user["id"], access_token=access_token)
    except Exception as e:
        logger.error(f"Error getting user agents: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error getting user agents: {str(e)}")


@router.post("/sessions")
async def create_session(
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """
    Create a new chat session.

    Args:
        current_user: The authenticated user, injected by Depends.
        credentials: The HTTP Authorization header credentials

    Returns:
        A new session object with a unique session_id.
    """
    try:
        # The user_id is now taken from the authenticated user's token
        user_id = current_user.get("id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User ID not found in token",
            )

        access_token = credentials.credentials if credentials else None
        # Create a new session using the agent_service
        session = await agent_service.create_session(user_id=user_id, access_token=access_token)

        # Return the session ID in the expected format
        return {
            "session_id": session.id,
            "user_id": session.user_id,
            "created_at": session.created_at,
            "expires_at": session.expires_at,
        }
    except Exception as e:
        logger.error(f"Error creating session: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error creating session: {str(e)}")


@router.get("/sessions/{session_id}")
async def get_session(
    session_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """
    Get session details

    Args:
        session_id: ID of the session to retrieve
        current_user: Authenticated user info from JWT token

    Returns:
        Session data if found
    """
    try:
        access_token = credentials.credentials if credentials else None
        session = await agent_service.get_session(session_id, access_token=access_token)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Verify the session belongs to the current user
        if session.get("user_id") != current_user["id"]:
            raise HTTPException(
                status_code=403, detail="Not authorized to access this session")

        return session
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error getting session: {str(e)}")


@router.get("/sessions/{session_id}/messages", response_model=PaginatedMessages)
async def get_session_messages(
    session_id: str,
    limit: int = 10,
    cursor: str = None,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """
    Get messages for a session with pagination

    Args:
        session_id: ID of the session
        limit: Maximum number of messages to return
        cursor: Cursor for pagination
        current_user: Authenticated user info from JWT token

    Returns:
        Paginated messages for the session
    """
    try:
        access_token = credentials.credentials if credentials else None
        # Verify the session belongs to the current user
        session = await agent_service.get_session(session_id, access_token=access_token)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        if session.user_id != current_user["id"]:
            raise HTTPException(
                status_code=403, detail="Not authorized to access this session")

        # Get the agent_id for the hotel_manager agent (or use a default)
        agent_id = await agent_service._get_agent_id("hotel_manager", access_token)

        # Get messages with pagination
        messages, next_cursor = await agent_service.get_messages(
            agent_id=agent_id,
            session_id=session_id,
            limit=limit,
            cursor=cursor,
            access_token=access_token
        )

        return {
            "messages": [
                Message(
                    id=msg.id,
                    content=msg.content,
                    role=msg.role,
                    user_id=msg.user_id,
                    agent_id=msg.agent_id,
                    created_at=msg.created_at,
                    updated_at=msg.updated_at,
                    metadata=msg.metadata or {}
                )
                for msg in messages
            ],
            "has_more": next_cursor is not None,
            "next_cursor": next_cursor
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error getting session messages: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error getting session messages: {str(e)}")


@router.delete("/sessions/{session_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_session(
    session_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(http_bearer),
):
    """
    Delete a session and its messages

    Args:
        session_id: ID of the session to delete
        current_user: Authenticated user info from JWT token
    """
    try:
        access_token = credentials.credentials if credentials else None
        # Verify the session belongs to the current user
        session = await agent_service.get_session(session_id, access_token=access_token)
        if not session:
            return  # Already deleted or never existed

        if session.get("user_id") != current_user["id"]:
            raise HTTPException(
                status_code=403, detail="Not authorized to delete this session")

        # Delete the session
        await agent_service.delete_session(session_id, access_token=access_token)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Error deleting session: {str(e)}")


@router.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test database connection
        agents_info = agent_service.get_available_agents()
        return {
            "status": "healthy",
            "database": "connected",
            "agents_available": len(agents_info),
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service unavailable: {str(e)}"
        )
