import { useState, useEffect, useCallback, useRef } from "react";
import { toast } from "sonner";
import Cookies from "js-cookie";
import { ChatHistory, Message } from "../types";
import {
	sendChatMessage,
	createChatSession,
	getSessionHistory,
} from "@/lib/api";

interface BackendMessage {
	id: string;
	content: string;
	role: "user" | "assistant";
	created_at: string;
	metadata?: Record<string, unknown>;
}

export const useChat = () => {
	const [chatHistory, setChatHistory] = useState<ChatHistory[]>([]);
	const [currentChat, setCurrentChat] = useState<Message[]>([]);
	const [inputMessage, setInputMessage] = useState("");
	const [selectedChat, setSelectedChat] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [sessionId, setSessionId] = useState<string | null>(null);
	const messagesEndRef = useRef<HTMLDivElement | null>(null);

	useEffect(() => {
		messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
	}, [currentChat]);

	useEffect(() => {
		const initChat = async () => {
			setIsLoading(true);
			try {
				// Check for existing session in cookie
				const storedSessionId = Cookies.get("sessionId");

				if (storedSessionId) {
					// Use existing session and load conversation history
					setSessionId(storedSessionId);
					console.log("Using existing session:", storedSessionId);

					// Try to load conversation history
					try {
						const history = await getSessionHistory(storedSessionId);
						if (history && history.length > 0) {
							// Convert backend messages to frontend format
							const messages: Message[] = history.map(
								(msg: BackendMessage, index: number) => ({
									id: msg.id || `msg-${index}`,
									content: msg.content,
									isUser: msg.role === "user",
									timestamp: new Date(msg.created_at || Date.now()),
									metadata: msg.metadata,
								})
							);
							// Reverse the order to show oldest messages first (chat UI convention)
							setCurrentChat(messages.reverse());
							console.log(
								"Loaded conversation history:",
								messages.length,
								"messages"
							);
						} else {
							// No history, show welcome message
							const welcomeMessage: Message = {
								id: "welcome",
								content:
									"Hello! I'm your HM Assistant. How can I help you today?",
								isUser: false,
								timestamp: new Date(),
							};
							setCurrentChat([welcomeMessage]);
						}
					} catch (error) {
						console.error("Error loading conversation history:", error);
						// Fallback to welcome message
						const welcomeMessage: Message = {
							id: "welcome",
							content:
								"Hello! I'm your HM Assistant. How can I help you today?",
							isUser: false,
							timestamp: new Date(),
						};
						setCurrentChat([welcomeMessage]);
					}
				} else {
					// Create new session only if none exists in cookie
					const { session_id } = await createChatSession();
					setSessionId(session_id);
					Cookies.set("sessionId", session_id, { expires: 7 });
					console.log("Created new session:", session_id);

					// Show welcome message for new session
					const welcomeMessage: Message = {
						id: "welcome",
						content: "Hello! I'm your HM Assistant. How can I help you today?",
						isUser: false,
						timestamp: new Date(),
					};
					setCurrentChat([welcomeMessage]);
				}
			} catch (error) {
				console.error("Error initializing chat:", error);
				toast.error("Failed to initialize chat session");
				const fallbackSessionId = `local-${Date.now()}`;
				setSessionId(fallbackSessionId);
				const errorMessage: Message = {
					id: "error",
					content:
						"I'm having trouble connecting to the server. Some features may be limited.",
					isUser: false,
					timestamp: new Date(),
				};
				setCurrentChat([errorMessage]);
			} finally {
				setIsLoading(false);
			}
		};
		initChat();
	}, []);

	const handleNewChat = useCallback(async () => {
		try {
			setIsLoading(true);
			const { session_id } = await createChatSession();
			setSessionId(session_id);
			Cookies.set("sessionId", session_id, { expires: 7 }); // Update cookie with new session
			setCurrentChat([]);
			setSelectedChat(null);
			toast.success("New chat started");
		} catch (error) {
			console.error("Error creating new chat:", error);
			toast.error("Failed to start new chat");
		} finally {
			setIsLoading(false);
		}
	}, []);

	const handleSendMessage = useCallback(
		async (message: string) => {
			if (!message.trim() || !sessionId) return;

			const userMessage: Message = {
				id: Date.now().toString(),
				content: message,
				isUser: true,
				timestamp: new Date(),
			};

			setCurrentChat((prev) => [...prev, userMessage]);
			setInputMessage("");
			setIsLoading(true);

			try {
				const response = await sendChatMessage(message, sessionId);

				const aiResponse: Message = {
					id: (Date.now() + 1).toString(),
					content: response.response,
					isUser: false,
					timestamp: new Date(),
					metadata: response.metadata,
				};

				setCurrentChat((prev) => [...prev, aiResponse]);

				if (!selectedChat) {
					const newChat: ChatHistory = {
						id: response.session_id,
						title:
							message.substring(0, 30) + (message.length > 30 ? "..." : ""),
						timestamp: new Date(),
					};
					setChatHistory((prev) => [newChat, ...prev]);
					setSelectedChat(response.session_id);
				}
			} catch (error) {
				console.error("Error sending message:", error);
				toast.error("Failed to send message. Please try again.");
			} finally {
				setIsLoading(false);
			}
		},
		[sessionId, selectedChat]
	);

	return {
		chatHistory,
		currentChat,
		inputMessage,
		setInputMessage,
		selectedChat,
		setSelectedChat,
		isLoading,
		sessionId,
		messagesEndRef,
		handleNewChat,
		handleSendMessage,
		setCurrentChat,
		setIsLoading,
	};
};
